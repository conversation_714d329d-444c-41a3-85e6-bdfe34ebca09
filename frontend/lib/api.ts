import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/auth/login';
    }
    return Promise.reject(error);
  }
);

export const authAPI = {
  register: (data: any) => api.post('/auth/register', data),
  login: (data: any) => api.post('/auth/login', data),
  logout: () => api.post('/auth/logout'),
  verifyEmail: (token: string) => api.post('/auth/verify-email', { token }),
  resendVerification: (email: string) => api.post('/auth/resend-verification', { email }),
  forgotPassword: (email: string) => api.post('/auth/forgot-password', { email }),
  resetPassword: (data: any) => api.post('/auth/reset-password', data),
};

export const userAPI = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data: any) => api.put('/users/profile', data),
  uploadDocuments: (data: FormData) => api.post('/users/documents', data, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
};

export const auctionAPI = {
  getAll: () => api.get('/auctions'),
  getById: (id: string) => api.get(`/auctions/${id}`),
  create: (data: any) => api.post('/auctions', data),
  update: (id: string, data: any) => api.put(`/auctions/${id}`, data),
  delete: (id: string) => api.delete(`/auctions/${id}`),
  placeBid: (id: string, amount: number) => api.post(`/auctions/${id}/bid`, { amount }),
};

export const tenderAPI = {
  getAll: () => api.get('/tenders'),
  getById: (id: string) => api.get(`/tenders/${id}`),
  create: (data: any) => api.post('/tenders', data),
  update: (id: string, data: any) => api.put(`/tenders/${id}`, data),
  delete: (id: string) => api.delete(`/tenders/${id}`),
  submitProposal: (id: string, data: any) => api.post(`/tenders/${id}/proposal`, data),
};

// Favorites/Watchlist API
export const favoritesAPI = {
  // Get user's favorites
  getFavorites: (params?: { type?: 'auction' | 'tender'; page?: number; limit?: number }) =>
    api.get('/favorites', { params }),
  
  // Add item to favorites
  addFavorite: (data: {
    itemType: 'auction' | 'tender';
    itemId: string;
    notes?: string;
    notifications?: {
      bidUpdates?: boolean;
      statusChanges?: boolean;
      endingSoon?: boolean;
    };
  }) => api.post('/favorites', data),
  
  // Remove item from favorites
  removeFavorite: (itemType: 'auction' | 'tender', itemId: string) =>
    api.delete(`/favorites/${itemType}/${itemId}`),
  
  // Update favorite settings
  updateFavorite: (itemType: 'auction' | 'tender', itemId: string, data: {
    notes?: string;
    notifications?: {
      bidUpdates?: boolean;
      statusChanges?: boolean;
      endingSoon?: boolean;
    };
  }) => api.put(`/favorites/${itemType}/${itemId}`, data),
  
  // Check if item is favorited
  checkFavorite: (itemType: 'auction' | 'tender', itemId: string) =>
    api.get(`/favorites/check/${itemType}/${itemId}`),
};

// Activity Logs API
export const activityAPI = {
  // Get user's activity logs
  getUserActivities: (params?: {
    page?: number;
    limit?: number;
    actionType?: string;
    severity?: 'low' | 'medium' | 'high' | 'critical';
    startDate?: string;
    endDate?: string;
  }) => api.get('/activity/user', { params }),
  
  // Get admin activity logs (admin only)
  getAdminActivities: (params?: {
    page?: number;
    limit?: number;
    userId?: string;
    actionType?: string;
    severity?: 'low' | 'medium' | 'high' | 'critical';
    startDate?: string;
    endDate?: string;
  }) => api.get('/activity/admin', { params }),
  
  // Get activity statistics
  getActivityStats: (params?: {
    period?: 'day' | 'week' | 'month' | 'year';
    userId?: string;
  }) => api.get('/activity/stats', { params }),
};

// Messaging API
export const messagesAPI = {
  // Conversation management
  conversations: {
    // Get user's conversations
    getAll: (params?: { page?: number; limit?: number; type?: string }) =>
      api.get('/messages/conversations', { params }),
    
    // Get conversation by ID
    getById: (conversationId: string) =>
      api.get(`/messages/conversations/${conversationId}`),
    
    // Create new conversation
    create: (data: {
      type: 'direct' | 'group' | 'support' | 'auction' | 'tender';
      participants: string[];
      title?: string;
      relatedItem?: {
        itemType: 'auction' | 'tender';
        itemId: string;
      };
    }) => api.post('/messages/conversations', data),
    
    // Update conversation
    update: (conversationId: string, data: {
      title?: string;
      settings?: {
        notifications?: boolean;
        autoArchive?: boolean;
      };
    }) => api.put(`/messages/conversations/${conversationId}`, data),
    
    // Add participants
    addParticipants: (conversationId: string, userIds: string[]) =>
      api.post(`/messages/conversations/${conversationId}/participants`, { userIds }),
    
    // Remove participants
    removeParticipants: (conversationId: string, userIds: string[]) =>
      api.delete(`/messages/conversations/${conversationId}/participants`, { data: { userIds } }),
    
    // Archive/unarchive conversation
    archive: (conversationId: string) =>
      api.post(`/messages/conversations/${conversationId}/archive`),
    
    unarchive: (conversationId: string) =>
      api.post(`/messages/conversations/${conversationId}/unarchive`),
  },
  
  // Message management
  messages: {
    // Get messages in conversation
    getByConversation: (conversationId: string, params?: {
      page?: number;
      limit?: number;
      before?: string;
      after?: string;
    }) => api.get(`/messages/conversations/${conversationId}/messages`, { params }),
    
    // Send message
    send: (conversationId: string, data: {
      content: string;
      type?: 'text' | 'image' | 'file';
      replyTo?: string;
      attachments?: {
        type: 'image' | 'file';
        url: string;
        filename?: string;
        size?: number;
      }[];
    }) => api.post(`/messages/conversations/${conversationId}/messages`, data),
    
    // Edit message
    edit: (conversationId: string, messageId: string, data: { content: string }) =>
      api.put(`/messages/conversations/${conversationId}/messages/${messageId}`, data),
    
    // Delete message
    delete: (conversationId: string, messageId: string) =>
      api.delete(`/messages/conversations/${conversationId}/messages/${messageId}`),
    
    // Mark messages as read
    markAsRead: (conversationId: string, messageIds: string[]) =>
      api.post(`/messages/conversations/${conversationId}/read`, { messageIds }),
    
    // Add reaction to message
    react: (conversationId: string, messageId: string, emoji: string) =>
      api.post(`/messages/conversations/${conversationId}/messages/${messageId}/react`, { emoji }),
    
    // Remove reaction from message
    unreact: (conversationId: string, messageId: string, emoji: string) =>
      api.delete(`/messages/conversations/${conversationId}/messages/${messageId}/react`, {
        data: { emoji }
      }),
  },
  
  // Search messages
  search: (params: {
    query: string;
    conversationId?: string;
    page?: number;
    limit?: number;
  }) => api.get('/messages/search', { params }),
};

// Admin API
export const adminAPI = {
  // Dashboard statistics
  getDashboardStats: () => api.get('/admin/dashboard'),
  
  // Pending accounts
  getPendingAccounts: () => api.get('/admin/pending-accounts'),
  approvePendingAccount: (accountId: string) => api.post(`/admin/pending-accounts/${accountId}/approve`),
  rejectPendingAccount: (accountId: string, reason?: string) => api.post(`/admin/pending-accounts/${accountId}/reject`, { reason }),
  
  // User management
  users: {
    getAll: (params?: { page?: number; limit?: number; search?: string; role?: string; status?: string }) =>
      api.get('/admin/users', { params }),
    getById: (userId: string) => api.get(`/admin/users/${userId}`),
    update: (userId: string, data: any) => api.put(`/admin/users/${userId}`, data),
    delete: (userId: string) => api.delete(`/admin/users/${userId}`),
    activate: (userId: string) => api.post(`/admin/users/${userId}/activate`),
    deactivate: (userId: string) => api.post(`/admin/users/${userId}/deactivate`),
  },
  
  // Auction management
  auctions: {
    getAll: (params?: { page?: number; limit?: number; status?: string; category?: string }) =>
      api.get('/admin/auctions', { params }),
    getById: (auctionId: string) => api.get(`/admin/auctions/${auctionId}`),
    approve: (auctionId: string) => api.post(`/admin/auctions/${auctionId}/approve`),
    reject: (auctionId: string, reason: string) => api.post(`/admin/auctions/${auctionId}/reject`, { reason }),
    suspend: (auctionId: string, reason: string) => api.post(`/admin/auctions/${auctionId}/suspend`, { reason }),
    delete: (auctionId: string) => api.delete(`/admin/auctions/${auctionId}`),
  },
  
  // Tender management
  tenders: {
    getAll: (params?: { page?: number; limit?: number; status?: string; category?: string }) =>
      api.get('/admin/tenders', { params }),
    getById: (tenderId: string) => api.get(`/admin/tenders/${tenderId}`),
    approve: (tenderId: string) => api.post(`/admin/tenders/${tenderId}/approve`),
    reject: (tenderId: string, reason: string) => api.post(`/admin/tenders/${tenderId}/reject`, { reason }),
    suspend: (tenderId: string, reason: string) => api.post(`/admin/tenders/${tenderId}/suspend`, { reason }),
    delete: (tenderId: string) => api.delete(`/admin/tenders/${tenderId}`),
  },
  
  // Individual tender methods (for backward compatibility)
  getTender: (tenderId: string) => api.get(`/admin/tenders/${tenderId}`),
  getTenderSubmissions: (tenderId: string, params?: { page?: number; limit?: number; status?: string }) =>
    api.get(`/admin/tenders/${tenderId}/submissions`, { params }),
  updateTenderStatus: (tenderId: string, data: { status: string; reason?: string }) =>
    api.put(`/admin/tenders/${tenderId}/status`, data),
  updateTenderSubmissionStatus: (tenderId: string, submissionId: string, data: { status: string; reason?: string }) =>
    api.put(`/admin/tenders/${tenderId}/submissions/${submissionId}/status`, data),
  
  // Reports and analytics
  reports: {
    getFinancialReport: (params?: { startDate?: string; endDate?: string; type?: string }) =>
      api.get('/admin/reports/financial', { params }),
    getUserReport: (params?: { startDate?: string; endDate?: string; type?: string }) =>
      api.get('/admin/reports/users', { params }),
    getActivityReport: (params?: { startDate?: string; endDate?: string; type?: string }) =>
      api.get('/admin/reports/activity', { params }),
    getAuctionReport: (params?: { startDate?: string; endDate?: string; type?: string }) =>
      api.get('/admin/reports/auctions', { params }),
    getTenderReport: (params?: { startDate?: string; endDate?: string; type?: string }) =>
      api.get('/admin/reports/tenders', { params }),
  },
  
  // System settings
  settings: {
    getAll: () => api.get('/admin/settings'),
    update: (data: any) => api.put('/admin/settings', data),
    backup: () => api.post('/admin/settings/backup'),
    restore: (backupId: string) => api.post(`/admin/settings/restore/${backupId}`),
  },
};

// Government API
export const governmentAPI = {
  // Tender management
  tenders: {
    getAll: (params?: { page?: number; limit?: number; status?: string; category?: string }) =>
      api.get('/government/tenders', { params }),
    getById: (tenderId: string) => api.get(`/tenders/${tenderId}`),
    create: (data: any) => api.post('/government/tenders', data),
    update: (tenderId: string, data: any) => api.put(`/tenders/${tenderId}`, data),
    delete: (tenderId: string) => api.delete(`/tenders/${tenderId}`),
    publish: (tenderId: string) => api.post(`/government/tenders/${tenderId}/publish`),
    close: (tenderId: string) => api.post(`/government/tenders/${tenderId}/close`),
    cancel: (tenderId: string, reason: string) => api.post(`/government/tenders/${tenderId}/cancel`, { reason }),
  },
  
  // Proposal management
  proposals: {
    getByTender: (tenderId: string, params?: { page?: number; limit?: number; status?: string }) =>
      api.get(`/government/tenders/${tenderId}/proposals`, { params }),
    getById: (tenderId: string, proposalId: string) => api.get(`/government/tenders/${tenderId}/proposals/${proposalId}`),
    evaluate: (tenderId: string, proposalId: string, data: any) => api.post(`/government/tenders/${tenderId}/proposals/${proposalId}/evaluate`, data),
    shortlist: (tenderId: string, proposalId: string) => api.post(`/government/tenders/${tenderId}/proposals/${proposalId}/shortlist`),
    award: (tenderId: string, proposalId: string, data: any) => api.post(`/government/tenders/${tenderId}/proposals/${proposalId}/award`, data),
    reject: (tenderId: string, proposalId: string, reason: string) => api.post(`/government/tenders/${tenderId}/proposals/${proposalId}/reject`, { reason }),
  },
  
  // Contract management
  contracts: {
    getAll: (params?: { page?: number; limit?: number; status?: string }) =>
      api.get('/government/contracts', { params }),
    getById: (contractId: string) => api.get(`/government/contracts/${contractId}`),
    create: (data: any) => api.post('/government/contracts', data),
    update: (contractId: string, data: any) => api.put(`/government/contracts/${contractId}`, data),
    approve: (contractId: string) => api.post(`/government/contracts/${contractId}/approve`),
    terminate: (contractId: string, reason: string) => api.post(`/government/contracts/${contractId}/terminate`, { reason }),
  },
  
  // Reports and analytics
  reports: {
    getDashboard: () => api.get('/government/reports/dashboard'),
    getTenderReport: (params?: { startDate?: string; endDate?: string; category?: string }) =>
      api.get('/government/reports/tenders', { params }),
    getContractReport: (params?: { startDate?: string; endDate?: string; status?: string }) =>
      api.get('/government/reports/contracts', { params }),
    getVendorReport: (params?: { startDate?: string; endDate?: string; performance?: string }) =>
      api.get('/government/reports/vendors', { params }),
  },
};

// Leaderboard API
export const leaderboardAPI = {
  getLeaderboard: (params?: { type?: 'auctions' | 'tenders' | 'overall'; period?: 'week' | 'month' | 'year' | 'all'; limit?: number }) =>
    api.get('/leaderboard', { params }),
  getUserRank: (userId?: string) => api.get(`/leaderboard/rank${userId ? `/${userId}` : ''}`),
  getTopBidders: (params?: { limit?: number; period?: 'week' | 'month' | 'year' | 'all' }) =>
    api.get('/leaderboard/bidders', { params }),
  getTopSellers: (params?: { limit?: number; period?: 'week' | 'month' | 'year' | 'all' }) =>
    api.get('/leaderboard/sellers', { params }),
};

// Company API
export const companyAPI = {
  getProfile: () => api.get('/company/profile'),
  updateProfile: (data: any) => api.put('/company/profile', data),
  getEmployees: (params?: { page?: number; limit?: number; role?: string }) =>
    api.get('/company/employees', { params }),
  addEmployee: (data: any) => api.post('/company/employees', data),
  updateEmployee: (employeeId: string, data: any) => api.put(`/company/employees/${employeeId}`, data),
  removeEmployee: (employeeId: string) => api.delete(`/company/employees/${employeeId}`),
  getAuctions: (params?: { page?: number; limit?: number; status?: string }) =>
    api.get('/company/auctions', { params }),
  getTenders: (params?: { page?: number; limit?: number; status?: string }) =>
    api.get('/company/tenders', { params }),
  getContracts: (params?: { page?: number; limit?: number; status?: string }) =>
    api.get('/company/contracts', { params }),
};

export default api;
