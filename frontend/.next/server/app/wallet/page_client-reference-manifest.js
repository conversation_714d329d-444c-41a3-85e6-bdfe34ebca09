globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/wallet/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/Footer.tsx":{"*":{"id":"(ssr)/./components/layout/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/Navbar.tsx":{"*":{"id":"(ssr)/./components/layout/Navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/error.tsx":{"*":{"id":"(ssr)/./app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/not-found.tsx":{"*":{"id":"(ssr)/./app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/payment/methods/page.tsx":{"*":{"id":"(ssr)/./app/payment/methods/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/wallet/page.tsx":{"*":{"id":"(ssr)/./app/wallet/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/messages/page.tsx":{"*":{"id":"(ssr)/./app/messages/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/help/page.tsx":{"*":{"id":"(ssr)/./app/help/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/status/page.tsx":{"*":{"id":"(ssr)/./app/status/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/feedback/page.tsx":{"*":{"id":"(ssr)/./app/feedback/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/billing/invoices/page.tsx":{"*":{"id":"(ssr)/./app/billing/invoices/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/auth/register/page.tsx":{"*":{"id":"(ssr)/./app/auth/register/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/user/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/auth/login/page.tsx":{"*":{"id":"(ssr)/./app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/company/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/company/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/auctions/[id]/page.tsx":{"*":{"id":"(ssr)/./app/auctions/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/auctions/page.tsx":{"*":{"id":"(ssr)/./app/user/auctions/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/auth/forgot-password/page.tsx":{"*":{"id":"(ssr)/./app/auth/forgot-password/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/government/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/government/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/government/tenders/page.tsx":{"*":{"id":"(ssr)/./app/government/tenders/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/government/create-tender/page.tsx":{"*":{"id":"(ssr)/./app/government/create-tender/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/government/tenders/[id]/page.tsx":{"*":{"id":"(ssr)/./app/government/tenders/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/government/tenders/[id]/edit/page.tsx":{"*":{"id":"(ssr)/./app/government/tenders/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/government/applications/page.tsx":{"*":{"id":"(ssr)/./app/government/applications/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/government/profile/page.tsx":{"*":{"id":"(ssr)/./app/government/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/guide/page.tsx":{"*":{"id":"(ssr)/./app/guide/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Desktop/brid1/frontend/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/layout/Footer.tsx":{"id":"(app-pages-browser)/./components/layout/Footer.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/layout/Navbar.tsx":{"id":"(app-pages-browser)/./components/layout/Navbar.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/ui/toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/contexts/AuthContext.tsx":{"id":"(app-pages-browser)/./contexts/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx":{"id":"(app-pages-browser)/./app/error.tsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx":{"id":"(app-pages-browser)/./app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/payment/methods/page.tsx":{"id":"(app-pages-browser)/./app/payment/methods/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx":{"id":"(app-pages-browser)/./app/wallet/page.tsx","name":"*","chunks":["app/wallet/page","static/chunks/app/wallet/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/messages/page.tsx":{"id":"(app-pages-browser)/./app/messages/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/help/page.tsx":{"id":"(app-pages-browser)/./app/help/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/status/page.tsx":{"id":"(app-pages-browser)/./app/status/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/feedback/page.tsx":{"id":"(app-pages-browser)/./app/feedback/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/billing/invoices/page.tsx":{"id":"(app-pages-browser)/./app/billing/invoices/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/auth/register/page.tsx":{"id":"(app-pages-browser)/./app/auth/register/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx":{"id":"(app-pages-browser)/./app/user/dashboard/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/auth/login/page.tsx":{"id":"(app-pages-browser)/./app/auth/login/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx":{"id":"(app-pages-browser)/./app/company/dashboard/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/auctions/[id]/page.tsx":{"id":"(app-pages-browser)/./app/auctions/[id]/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx":{"id":"(app-pages-browser)/./app/user/auctions/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/auth/forgot-password/page.tsx":{"id":"(app-pages-browser)/./app/auth/forgot-password/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/dashboard/page.tsx":{"id":"(app-pages-browser)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx":{"id":"(app-pages-browser)/./app/government/dashboard/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/page.tsx":{"id":"(app-pages-browser)/./app/government/tenders/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx":{"id":"(app-pages-browser)/./app/government/create-tender/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/page.tsx":{"id":"(app-pages-browser)/./app/government/tenders/[id]/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx":{"id":"(app-pages-browser)/./app/government/tenders/[id]/edit/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx":{"id":"(app-pages-browser)/./app/government/applications/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/government/profile/page.tsx":{"id":"(app-pages-browser)/./app/government/profile/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/guide/page.tsx":{"id":"(app-pages-browser)/./app/guide/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/Desktop/brid1/frontend/":[],"/Users/<USER>/Desktop/brid1/frontend/app/page":[],"/Users/<USER>/Desktop/brid1/frontend/app/layout":["static/css/app/layout.css"],"/Users/<USER>/Desktop/brid1/frontend/app/error":[],"/Users/<USER>/Desktop/brid1/frontend/app/loading":[],"/Users/<USER>/Desktop/brid1/frontend/app/not-found":[],"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page":[]}}